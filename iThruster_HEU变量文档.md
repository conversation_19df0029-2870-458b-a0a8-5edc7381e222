# iCAN1NET_HEU 模块变量文档

本文档详细介绍了iCAN1NET_HEU模块与MOOS数据库交互的变量，包括订阅的变量和发布的变量。

## 1. 订阅的变量

iCAN1NET_HEU模块订阅以下MOOS变量：

| 变量名          | MOOS名称            | 数据类型 | 描述                           |
|----------------|-------------------|---------|-------------------------------|
| T1             | DESIRED_Percent   | double  | 主推进器控制量                  |
| RUD_UP         | DESIRED_RUDDER_UP | double  | 垂直舵机UP控制角度（使用时会乘以-1） |
| RUD_DOWN       | DESIRED_RUDDER_DOWN | double  | 垂直舵机DOWN控制角度（使用时会乘以-1） |
| RUD_LEFT       | DESIRED_WING_LEFT | double  | 水平舵机LEFT控制角度             |
| RUD_RIGHT      | DESIRED_WING_RIGHT | double  | 水平舵机RIGHT控制角度（使用时会乘以-1） |

## 2. 发布的变量
iCAN1NET_HEU模块发布以下MOOS变量：

| 变量名          | 数据类型 | 单位    | 描述                           |
|----------------|---------|---------|-------------------------------|
| AUV_RPM          | double  | r/min   | 主推进器转速                    |
| AUV_VoltT1         | double  | V       | 主推进器电压                    |
| AUV_CurrentT1      | double  | A       | 主推进器电流                    |
| LEAK1          | double  | -       | 漏水检测状态（0:无漏水, 8:轻微漏水, 16:中度漏水, 24:严重漏水） |

## 3. 数据处理说明

### 订阅变量处理
- 主推进器控制量(T1)：直接使用
- 垂直舵机UP控制角度(RUD_UP)：取反使用(乘以-1)
- 垂直舵机DOWN控制角度(RUD_DOWN)：取反使用(乘以-1)
- 水平舵机LEFT控制角度(RUD_LEFT)：直接使用
- 水平舵机RIGHT控制角度(RUD_RIGHT)：取反使用(乘以-1)

### 发布变量处理
- 主推状态：通过解析网络收到的CAN报文获取
- 漏水状态：解析Frame[12]字节，根据值确定漏水级别：
  - 0x00: 无漏水(0)
  - 0x10: 轻微漏水(8)
  - 0x20: 中度漏水(16)
  - 0x30: 严重漏水(24) 