/************************************************************/
/*    NAME: zhaoqinchao                                   */
/*    ORGN: HEU                                             */
/*    FILE: iThruster_HEU.h                                 */
/*    DATE: 2025/7/10                                      */
/************************************************************/

#ifndef _iThruster_HEU_H_
#define _iThruster_HEU_H_

#include "MOOS/libMOOS/MOOSLib.h"
#include "MBUtils.h"
#include "BlueSocket.h"
#include "pthread.h"
const size_t FRAME_LEN = 13;

class iThruster_HEU : public CMOOSApp
{
public:
	iThruster_HEU();
	~iThruster_HEU();
	bool OnStartUp();
	bool Iterate();
	bool OnNewMail(MOOSMSG_LIST &NewMail);
	bool OnConnectToServer();
	bool UpdateLocalMOOSVariables(MOOSMSG_LIST & NewMail);

private:
	void RecvFrame();
	void SendFrame(std::vector<uint8_t> Frame);
	void ParseFrame(std::vector<uint8_t> Frame);
	void PublishAlarm(int code, const std::string& level, const std::string& note);
	void controlFrame();
	void servoControlFrame();  // 舵机控制函数
	void sendServoAngle(uint8_t servoNode, double angle);  // 单个舵机角度控制
	void sendServoParameterCommand(uint8_t servoNode, uint8_t command, uint16_t data);  // 舵机参数设置命令
	void sendServoAngleByC2(uint8_t servoNode, double angle);  // 使用0xC2命令的角度控制
	bool TypeChoice(std::string param);
	static void* RecvFrameWrapper(void* arg);
	void fourshSendcontrolmsg();
	void SetEnable();
	void SetDisable();
private:
	// Socket通信变量
	BlueSocket RecvSock;    // 接收Socket
	BlueSocket SendSock;    // 发送Socket

	// 网络重试配置参数
	int m_iMaxRetries;      // 最大重试次数
	int m_iRetryDelay;      // 重试延迟时间（秒）

	// 线程管理
	pthread_t m_RecvTid;    // 接收线程ID
	bool m_bThreadRunning;  // 线程运行标志

	// 推进器控制变量
	double dT1;             // 推进器转速值

	// 舵机控制变量
	double dRudderUp;       // 垂直舵机UP角度值
	double dRudderDown;     // 垂直舵机DOWN角度值
	double dRudderLeft;     // 水平舵机LEFT角度值
	double dRudderRight;    // 水平舵机RIGHT角度值

	// 四个舵机的45度转换后角度值
	double dRudderA;        // 左上舵机（舵机1）
	double dRudderB;        // 右上舵机（舵机2）
	double dRudderC;        // 左下舵机（舵机3）
	double dRudderD;        // 右下舵机（舵机4）

	// 姿态角变量
	double thetaPitch;      // 俯仰角
	double thetaYaw;        // 偏航角

	// 状态变量
	int iterate;            // 迭代计数器
	double dLEAK;           // 漏水检测状态
};

#endif
