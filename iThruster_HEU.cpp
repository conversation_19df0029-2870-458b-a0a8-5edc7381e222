/***********************************************************/
/*    NAME: DYF                                            */
/*    ORGN: HEU                                            */
/*    FILE: iThruster_HEU.cpp                              */
/*    DATE: 2023/11/28                                     */
/*    DESC: 发送至CANHUB控制主推、舵机和漏水检测               */
/*    VERSION: v2.1.0                                      */
/*                                                         */
/*    修改历史:                                              */
/*    v2.0.0 (2025/07/11) - 增加内容:                      */
/*      - 添加网络重试机制和配置参数                          */
/*      - 完善漏水检测系统(MCU2+LEAK帧分级检测)              */
/*      - 统一告警系统(AUV_WARN_MSG格式)                    */
/*      - 修正推进器控制协议                                 */
/*      - 添加舵机45度坐标转换                               */
/***********************************************************/

#include "iThruster_HEU.h"
#include <errno.h>
#include <time.h>
#include <cmath>
#include <unistd.h>

using namespace std;

iThruster_HEU::iThruster_HEU()
{
	SetAppFreq(10);
	SetCommsFreq(10);
	dT1 = 0;
	dRudderUp = 0;
	dRudderDown = 0;
	dRudderLeft = 0;
	dRudderRight = 0;
	iterate = 0;
	dLEAK = 0;
	thetaPitch = 0;
	thetaYaw = 0;
	// 初始化四个舵机角度值
	dRudderA = 0;
	dRudderB = 0;
	dRudderC = 0;
	dRudderD = 0;
	// 初始化网络重试配置的默认值
	m_iMaxRetries = 5; // 默认最大重试5次
	m_iRetryDelay = 2; // 默认重试延迟2秒

	// 初始化线程控制标志
	m_bThreadRunning = false;

	// 初始化推进器和舵机控制标志变量（已简化，不再使用标志位）
	// m_bSetThrusterSpeed = false;    // 已移除：未使用
}

iThruster_HEU::~iThruster_HEU()
{
	// 清理接收线程
	if (m_bThreadRunning)
	{
		MOOSTrace("正在停止接收线程...\n");

		// 设置线程停止标志
		m_bThreadRunning = false;

		// 等待线程结束，最多等待3秒
		struct timespec timeout;
		clock_gettime(CLOCK_REALTIME, &timeout);
		timeout.tv_sec += 3;  // 3秒超时

		int result = pthread_timedjoin_np(m_RecvTid, NULL, &timeout);
		if (result == ETIMEDOUT)
		{
			MOOSTrace("线程等待超时，强制取消线程\n");
			pthread_cancel(m_RecvTid);
			pthread_join(m_RecvTid, NULL);  // 等待取消完成
		}
		else if (result != 0)
		{
			MOOSTrace("线程等待失败，强制取消线程\n");
			pthread_cancel(m_RecvTid);
			pthread_join(m_RecvTid, NULL);
		}

		MOOSTrace("接收线程已停止\n");
	}
}

bool iThruster_HEU::OnStartUp()
{
	string sRecvIP;
	if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
	{
		MOOSTrace("cannot get RecvIP \n");
		sRecvIP = "0.0.0.0";
	}

	int iRecvPort;
	if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
	{
		MOOSTrace("cannot get RecvPort \n");
		PublishAlarm(0, "FATAL", "MOOS File Read Failed");
	}

	string sDestIP;
	if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP))
	{
		MOOSTrace("cannot get DestIP \n");
		PublishAlarm(0, "FATAL", "MOOS File Read Failed");
	}

	int iDestPort;
	if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
	{
		MOOSTrace("cannot get DestPort \n");
		PublishAlarm(0, "FATAL", "MOOS File Read Failed");
	}

	// 读取网络重试配置参数
	if (!m_MissionReader.GetConfigurationParam("MaxRetries", m_iMaxRetries))
	{
		MOOSTrace("MaxRetries not specified, using default value: %d\n", m_iMaxRetries);
	}
	else
	{
		MOOSTrace("MaxRetries configured: %d\n", m_iMaxRetries);
	}

	if (!m_MissionReader.GetConfigurationParam("RetryDelay", m_iRetryDelay))
	{
		MOOSTrace("RetryDelay not specified, using default value: %d seconds\n", m_iRetryDelay);
	}
	else
	{
		MOOSTrace("RetryDelay configured: %d seconds\n", m_iRetryDelay);
	}

	// 验证配置参数的合理性
	if (m_iMaxRetries < 0)
	{
		MOOSTrace("MaxRetries cannot be negative, setting to 0\n");
		m_iMaxRetries = 0;
	}
	if (m_iRetryDelay < 0)
	{
		MOOSTrace("RetryDelay cannot be negative, setting to 1 second\n");
		m_iRetryDelay = 1;
	}

	// 使用带重试机制的socket连接
	MOOSTrace("Attempting to open receive socket with retry mechanism...\n");
	if (!RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, m_iMaxRetries, m_iRetryDelay))
	{
		MOOSTrace("Failed to open receive socket after retries\n");
		PublishAlarm(1, "FATAL", "Binding Failed");
	}

	MOOSTrace("Attempting to bind receive socket with retry mechanism...\n");
	if (!RecvSock.RebindSocket(m_iMaxRetries, m_iRetryDelay))
	{
		MOOSTrace("Failed to bind receive socket after retries\n");
		PublishAlarm(1, "FATAL", "Binding Failed");
	}

	MOOSTrace("Attempting to open send socket with retry mechanism...\n");
	if (!SendSock.OpenSocketWithRetry(sDestIP, iDestPort, m_iMaxRetries, m_iRetryDelay))
	{
		MOOSTrace("Failed to open send socket after retries\n");
		PublishAlarm(2, "FATAL", "Socket Open Failed");
	}

	// 设置线程运行标志
	m_bThreadRunning = true;

	if (pthread_create(&m_RecvTid, NULL, &iThruster_HEU::RecvFrameWrapper, this) != 0)
	{
		MOOSTrace("cannot create recv thread \n");
		PublishAlarm(3, "FATAL", "Thread Create Failed");
		m_bThreadRunning = false;  // 线程创建失败，重置标志
	}

	AddMOOSVariable("T1", "DESIRED_Percent", "", 0.1);
	AddMOOSVariable("RUD_UP", "DESIRED_RUDDER_UP", "", 0.1);
	AddMOOSVariable("RUD_DOWN", "DESIRED_RUDDER_DOWN", "", 0.1);
	AddMOOSVariable("RUD_LEFT", "DESIRED_WING_LEFT", "", 0.1);
	AddMOOSVariable("RUD_RIGHT", "DESIRED_WING_RIGHT", "", 0.1);
	AddMOOSVariable("CONTROL_MSG", "CONTROL_MSG", "", 0.1);

	RegisterMOOSVariables();
	return true;
}

bool iThruster_HEU::OnNewMail(MOOSMSG_LIST &NewMail)
{
	// MOOSTrace("This is OnNewMail\n");
	UpdateLocalMOOSVariables(NewMail);
	// MOOSTrace("NewMail Size = %d\n", NewMail.size());
	CMOOSVariable *p = NULL;

	p = GetMOOSVar("T1");
	if (p->IsFresh())
	{
		dT1 = (*p).GetDoubleVal();
		p->SetFresh(false);
	}

	p = GetMOOSVar("RUD_UP");
	if (p->IsFresh())
	{
		dRudderUp = (*p).GetDoubleVal();
		dRudderUp = -1 * dRudderUp;
		p->SetFresh(false);
	}

	p = GetMOOSVar("RUD_DOWN");
	if (p->IsFresh())
	{
		dRudderDown = -(*p).GetDoubleVal();
		dRudderDown = -1 * dRudderDown;
		p->SetFresh(false);
	}

	p = GetMOOSVar("RUD_LEFT");
	if (p->IsFresh())
	{
		dRudderLeft = (*p).GetDoubleVal();
		p->SetFresh(false);
	}
	p = GetMOOSVar("RUD_RIGHT");
	if (p->IsFresh())
	{
		dRudderRight = -(*p).GetDoubleVal();
		p->SetFresh(false);
	}

	// 处理CONTROL_MSG命令（TypeChoice格式）
	p = GetMOOSVar("CONTROL_MSG");
	if (p->IsFresh())
	{
		std::string control_msg = p->GetStringVal();
		MOOSTrace("OnNewMail: 收到CONTROL_MSG = [%s]\n", control_msg.c_str());
		TypeChoice(control_msg);
		p->SetFresh(false);
	}

	return (true);
}

// 静态包装函数用于pthread
void* iThruster_HEU::RecvFrameWrapper(void* arg)
{
	iThruster_HEU* instance = static_cast<iThruster_HEU*>(arg);
	instance->RecvFrame();
	return nullptr;
}

void iThruster_HEU::RecvFrame()
{
	MOOSTrace("接收线程已启动\n");

	while (m_bThreadRunning)
	{
		vector<uint8_t> Frame;
		int n = RecvSock.RecvBinary(Frame, FRAME_LEN);
		if (n <= 0)
		{
			MOOSTrace("RecvFrame: 接收数据失败，错误码=%d\n", n);
			PublishAlarm(4, "WARNING", "Frame Receive Failed");
			// 不再使用exit，而是优雅退出线程
			MOOSTrace("RecvFrame: 接收线程即将退出\n");
			break;  // 退出循环，结束线程
		}

		ParseFrame(Frame);

		// MOOSTrace("FRAME %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",Frame[0],Frame[1],Frame[2],Frame[3],Frame[4],Frame[5],Frame[6],Frame[7],Frame[8],Frame[9],Frame[10],Frame[11],Frame[12]);
	}

	MOOSTrace("接收线程已退出\n");
}

void iThruster_HEU::PublishAlarm(int code, const std::string& level, const std::string& note)
{
	std::string alarm = "ID=iThruster_HEU;CODE=" + std::to_string(code) + ";LEVEL=" + level + ";NOTE=" + note;
	Notify("AUV_WARN_MSG", alarm);
	MOOSTrace("告警发布: %s\n", alarm.c_str());
}

void iThruster_HEU::ParseFrame(vector<uint8_t> Frame)
{
	if (Frame.size() != FRAME_LEN)
	{
		MOOSTrace("invalid frame length \n");
		PublishAlarm(6, "WARNING", "Invalid Frame Length");
		return;
	}

	vector<uint8_t> FrameHeader(Frame.begin(), Frame.begin() + 5);

	// 根据1+4文档定义的推进器反馈帧
	vector<uint8_t> MCU1Header = {0x08, 0x18, 0xEF, 0x10, 0x20}; // MCU1状态反馈帧1 (0x18EF1020)
	vector<uint8_t> LEAKHeader = {0x08, 0x00, 0x00, 0x00, 0x12}; // LEAK1

	if (FrameHeader == MCU1Header)
	{

		// Byte1-2: 电机母线电流 (Frame[5-6])
		int16_t current = (Frame[6] << 8) | Frame[5]; // 小端格式，有符号数
		double CurrentT1 = (double)current;

		// Byte7-8: 电机转速 (Frame[11-12])
		int16_t rpm = (Frame[12] << 8) | Frame[11]; // 小端格式，有符号数
		double RpmT1 = (double)rpm;

		MOOSTrace("MCU1解析: 转速=%d rpm, 电流=%d A\n", rpm, current);

		// 电流告警检查
		double abs_current = abs(current); // 使用绝对值检查电流
		if (abs_current >= 600.0)
		{
			PublishAlarm(24, "FATAL", "Thruster Overcurrent Level 2");
		}
		else if (abs_current >= 550.0)
		{
			PublishAlarm(25, "FATAL", "Thruster Overcurrent Level 1");
		}

		// 转速告警检查
		double abs_rpm = abs(rpm); // 使用绝对值检查转速
		if (abs_rpm >= 8000.0)
		{
			PublishAlarm(26, "FATAL", "Thruster Overspeed Level 2");
		}
		else if (abs_rpm >= 6000.0)
		{
			PublishAlarm(27, "WARNING", "Thruster Overspeed Level 1");
		}

		// 发布推进器运行状态到MOOS数据库
		Notify("AUV_RPM", RpmT1);			// 推进器当前转速
		Notify("AUV_CurrentT1", CurrentT1); // 推进器当前电流

		// 注意：电压信息在MCU2帧中，这里不解析
	}

	// MCU2帧 - 电压、温度和漏水检测
	vector<uint8_t> MCU2Header = {0x08, 0x18, 0xEF, 0x11, 0x20}; // MCU2状态反馈帧2 (0x18EF1120)

	if (FrameHeader == MCU2Header)
	{
		// 按照1+4文档MCU2帧格式解析
		// Byte1-2: 电机母线电压 (Frame[5-6], 单位0.1V, 无符号数)
		uint16_t voltage_raw = (Frame[6] << 8) | Frame[5]; // 小端格式
		double VoltT1 = (double)voltage_raw * 0.1;		   // 实际电压=值×0.1

		// Byte6: 漏水检测 (Frame[10])
		uint8_t leak_status = Frame[10];
		if (leak_status == 0)
		{
			dLEAK = 0; // 正常
		}
		else if (leak_status == 1)
		{
			dLEAK = 64; // 尾端漏水故障 - 符合AUV_WARN_MSG故障等级编码规范
			PublishAlarm(6, "FATAL", "Tail Water Leak Detected in MCU2");
		}

		// Byte7: 控制器温度 (Frame[11], 实际温度=值-50)
		uint8_t controller_temp_raw = Frame[11];
		double controller_temp = (double)controller_temp_raw - 50.0;

		// Byte8: 电机温度 (Frame[12], 实际温度=值-50)
		uint8_t motor_temp_raw = Frame[12];
		double motor_temp = (double)motor_temp_raw - 50.0;

		MOOSTrace("MCU2解析: 电压=%.1fV, 漏水=%d, 控制器温度=%.1f℃, 电机温度=%.1f℃\n",
				  VoltT1, leak_status, controller_temp, motor_temp);

		// 发布推进器状态信息到MOOS数据库
		Notify("AUV_VoltT1", VoltT1);	  // 推进器母线电压
		Notify("AUV_TempT1", motor_temp); // 推进器电机温度
		Notify("LEAK1", dLEAK);			  // 漏水检测状态
	}

	if (FrameHeader == LEAKHeader)
	{
		MOOSTrace("LEAK帧检测: Frame[12]=0x%02X\n", Frame[12]);

		if (Frame[12] == 0x00)
		{
			dLEAK = 0; // 无漏水
		}
		else if (Frame[12] == 0x10)
		{
			dLEAK = 8; // 轻微漏水
			PublishAlarm(7, "WARNING", "Minor Water Leak Detected");
		}
		else if (Frame[12] == 0x20)
		{
			dLEAK = 16; // 中度漏水
			PublishAlarm(8, "FATAL", "Moderate Water Leak Detected");
		}
		else if (Frame[12] == 0x30)
		{
			dLEAK = 24; // 严重漏水
			PublishAlarm(9, "FATAL", "Severe Water Leak Detected");
		}
		else if (Frame[12] == 0x40)
		{
			dLEAK = 32; // 致命漏水
			PublishAlarm(10, "FATAL", "Critical Water Leak Detected");
		}
		else
		{
			// 未知漏水状态
			MOOSTrace("LEAK帧: 未知漏水状态值 0x%02X\n", Frame[12]);
			PublishAlarm(11, "WARNING", "Unknown Leak Status");
		}

		MOOSTrace("LEAK帧解析: dLEAK = %f\n", dLEAK);
		Notify("LEAK1", dLEAK);
	}

	// 舵机反馈帧解析 (0x1FFFF381-384)
	vector<uint8_t> Servo1Header = {0x08, 0x1F, 0xFF, 0xF3, 0x81}; // 舵机1反馈 (0x1FFFF381)
	vector<uint8_t> Servo2Header = {0x08, 0x1F, 0xFF, 0xF3, 0x82}; // 舵机2反馈 (0x1FFFF382)
	vector<uint8_t> Servo3Header = {0x08, 0x1F, 0xFF, 0xF3, 0x83}; // 舵机3反馈 (0x1FFFF383)
	vector<uint8_t> Servo4Header = {0x08, 0x1F, 0xFF, 0xF3, 0x84}; // 舵机4反馈 (0x1FFFF384)

	if (FrameHeader == Servo1Header || FrameHeader == Servo2Header ||
		FrameHeader == Servo3Header || FrameHeader == Servo4Header)
	{
		// 确定舵机编号
		uint8_t servoNum = Frame[4] & 0x0F; // 取CAN ID最后一个字节的低4位

		// Byte0-1: 当前角度 (Frame[5-6], int16, 角度×10)
		int16_t angle_raw = (Frame[5] << 8) | Frame[6]; // 大端格式
		double current_angle = (double)angle_raw / 10.0; // 实际角度

		// Byte2-3: 当前电流 (Frame[7-8], uint16, 电流×10, 单位0.1A)
		uint16_t current_raw = (Frame[7] << 8) | Frame[8]; // 大端格式
		double current_current = (double)current_raw / 10.0; // 实际电流(A)

		// Byte4-5: 当前温度 (Frame[9-10], int16, 温度×10, 单位0.1℃)
		int16_t temp_raw = (Frame[9] << 8) | Frame[10]; // 大端格式
		double current_temp = (double)temp_raw / 10.0; // 实际温度(℃)

		// Byte6-7: 故障码 (Frame[11-12], uint16)
		uint16_t fault_code = (Frame[11] << 8) | Frame[12]; // 大端格式

		MOOSTrace("舵机%d反馈: 角度=%.1f°, 电流=%.1fA, 温度=%.1f℃, 故障码=0x%04X\n",
				  servoNum, current_angle, current_current, current_temp, fault_code);

		// 发布MOOS变量
		string angle_var = "SERVO" + std::to_string(servoNum) + "_ANGLE";
		string current_var = "SERVO" + std::to_string(servoNum) + "_CURRENT";
		string temp_var = "SERVO" + std::to_string(servoNum) + "_TEMP";
		string fault_var = "SERVO" + std::to_string(servoNum) + "_FAULT";

		Notify(angle_var, current_angle);
		Notify(current_var, current_current);
		Notify(temp_var, current_temp);
		Notify(fault_var, (double)fault_code);

		// 故障检查和告警
		if (fault_code != 0xFFFF) // 0xFFFF表示所有位都正常
		{
			string fault_note = "Servo" + std::to_string(servoNum) + " Fault Code: 0x" +
								std::to_string(fault_code);
			PublishAlarm(12 + servoNum, "WARNING", fault_note); // CODE=13,14,15,16对应舵机1,2,3,4
		}
	}
}

bool iThruster_HEU::Iterate()
{
	// 推进器控制：每次Iterate都发送当前的dT1值
	// 直接使用当前的dT1值（来自OnNewMail或TypeChoice）
	controlFrame();
	MOOSTrace("Iterate: 发送推进器转速控制 %f RPM\n", dT1);

	// - 推进器：使用dT1值发送controlFrame()
	// - 舵机：使用dRudderUp/Down/Left/Right进行45度转换后发送servoControlFrame()
	// 旧的舵机控制代码已注释，现在使用1+4舵机协议
	// vector<uint8_t> FrameRud = {0x08, 0x00, 0x00, 0x05, 0x33,
	//							0xFA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFA}; // 舵机上下左右是Frame[6789]
	// int nRudSign = 0;

	// 姿态角计算 - 使用平均值来计算偏航角和俯仰角
	thetaYaw = (dRudderLeft + dRudderRight) / 2.0; // 偏航角（左右控制平均值）
	thetaPitch = (dRudderUp + dRudderDown) / 2.0;  // 俯仰角（上下控制平均值）
	MOOSTrace("thetaYaw=%lf (平均值: Left=%lf, Right=%lf)\n", thetaYaw, dRudderLeft, dRudderRight);
	MOOSTrace("thetaPitch=%lf (平均值: Up=%lf, Down=%lf)\n", thetaPitch, dRudderUp, dRudderDown);

	// 四舵机角度分解计算（45度坐标系转换）
	dRudderA = (thetaYaw + thetaPitch) / sqrt(2);  // 左上舵机（原上舵）
	dRudderB = (thetaYaw - thetaPitch) / sqrt(2);  // 右上舵机（原右翼）
	dRudderC = (-thetaYaw + thetaPitch) / sqrt(2); // 左下舵机（原左翼）
	dRudderD = (-thetaYaw - thetaPitch) / sqrt(2); // 右下舵机（原下舵）

	MOOSTrace("dRudderA=%lf\n", dRudderA);
	MOOSTrace("dRudderB=%lf\n", dRudderB);
	MOOSTrace("dRudderC=%lf\n", dRudderC);
	MOOSTrace("dRudderD=%lf\n", dRudderD);

	// 发送舵机控制指令
	servoControlFrame();

	// 发布当前舵机控制角度到MOOS数据库
	Notify("DESIRED_RUDDER_UP", dRudderUp);
	Notify("DESIRED_RUDDER_DOWN", dRudderDown);
	Notify("DESIRED_WING_LEFT", dRudderLeft);
	Notify("DESIRED_WING_RIGHT", dRudderRight);

	// 旧的舵机控制代码已注释，现在使用1+4舵机协议中的servoControlFrame()函数
	/*
	// 舵机；Frame[10]为符号位
	if (dRudderA < 0)
	{
		// 符号位
		nRudSign += 1;
		double dRudderA = -dRudderA;
		FrameRud[6] = (unsigned char)(dRudderA);
	}
	else
	{
		FrameRud[6] = (unsigned char)(dRudderA);
	}

	if (dRudderD < 0)
	{
		nRudSign += 2;
		double dRudderD = -dRudderD;
		FrameRud[7] = (unsigned char)(dRudderD);
	}
	else
	{
		FrameRud[7] = (unsigned char)(dRudderD);
	}

	if (dRudderC < 0)
	{
		nRudSign += 4;
		double dRudderC = -dRudderC;
		FrameRud[8] = (unsigned char)(dRudderC);
	}
	else
	{
		FrameRud[8] = (unsigned char)(dRudderC);
	}

	if (dRudderB < 0)
	{
		nRudSign += 8;
		int dRudderB = -dRudderB;
		FrameRud[9] = (unsigned char)(dRudderB);
	}
	else
	{
		FrameRud[9] = (unsigned char)(dRudderB);
	}
	MOOSTrace("Up_Rudder %u \n", FrameRud[6]);
	MOOSTrace("Down_Rudder %u \n", FrameRud[7]);
	MOOSTrace("Right_Rudder %u \n", FrameRud[9]);
	MOOSTrace("Left_Rudder %u \n", FrameRud[8]);

	// FrameRud[6] = static_cast<unsigned char>(dRudderUp);
	// FrameRud[7] = static_cast<unsigned char>(dRudderDown);
	// FrameRud[8] = static_cast<unsigned char>(dRudderLeft);
	// FrameRud[9] = static_cast<unsigned char>(dRudderRight);
	FrameRud[11] = (unsigned char)(nRudSign);

	SendFrame(FrameRud);
	MOOSPause(50);
	MOOSPause(50);

	MOOSTrace(" send frame  \n");
	*/

	return true;
}

void iThruster_HEU::SendFrame(vector<uint8_t> Frame)
{
	int a = SendSock.SendBinary(Frame);
	if (a < 0)
	{
		MOOSTrace("SendFrame: 发送数据失败\n");
		PublishAlarm(5, "WARNING", "Frame Send Failed");
	}
	// MOOSTrace ("a = %d \n",a);
	iterate++;
	// MOOSTrace ("iterate = %d \n",iterate);
}

bool iThruster_HEU::OnConnectToServer()
{
	// MOOSTrace("This is OnConnectToServer function\n");
	RegisterMOOSVariables();
	return (true);
}

bool iThruster_HEU::UpdateLocalMOOSVariables(MOOSMSG_LIST &NewMail)
{
	MOOSVARMAP::iterator p;
	for (p = m_MOOSVars.begin(); p != m_MOOSVars.end(); p++)
	{
		CMOOSVariable &rVar = p->second;
		CMOOSMsg Msg;
		if (m_Comms.PeekMail(NewMail, rVar.GetSubscribeName(), Msg, false, true))
		{
			rVar.Set(Msg);
			rVar.SetFresh(true);
		}
	}
	return (true);
}
void iThruster_HEU::controlFrame()
{
	std::vector<uint8_t> Frame(13, 0);
	// 设置数据长度（第0位是08，代表八位数据）
	Frame[0] = 0x08; // 8字节数据长度
	// 设置CAN ID - VCU1控制指令帧 (0x18EF2010)
	Frame[1] = 0x18; // CAN ID字节0
	Frame[2] = 0xEF; // CAN ID字节1
	Frame[3] = 0x20; // CAN ID字节2
	Frame[4] = 0x10; // CAN ID字节3

	// 按照VCU1协议设置数据部分
	// Byte1: 预充状态(Bit1=1) + 使能命令(Bit3=1) = 0x0A
	Frame[5] = 0x0A; // 预充=1, 使能=1 (Bit1=1, Bit3=1)

	// Byte2: 动作命令 (1=转速模式)
	Frame[6] = 0x01; // 转速模式

	// Byte3-4: 期望扭矩百分比（转速模式时可以为0）
	Frame[7] = 0x00; // 扭矩百分比高字节
	Frame[8] = 0x00; // 扭矩百分比低字节

	// Byte5-6: 保留字节
	Frame[9] = 0x00;  // 保留
	Frame[10] = 0x00; // 保留

	// Byte7-8: 期望转速 (有符号数，单位rpm)
	// TypeChoice 接收 RPM 值直接存到 dT1
	int16_t targetRPM = (int16_t)dT1; // 直接转换为16位有符号整数

	// 将转速值按小端格式存储
	Frame[11] = (uint8_t)(targetRPM & 0xFF);		// 转速低字节
	Frame[12] = (uint8_t)((targetRPM >> 8) & 0xFF); // 转速高字节

	MOOSTrace("controlFrame: 期望转速=%d rpm\n", targetRPM);
	MOOSTrace("controlFrame: Frame[11]=%02X, Frame[12]=%02X\n", Frame[11], Frame[12]);
	// 发送控制帧
	SendFrame(Frame);
	MOOSTrace("controlFrame: VCU1推进器控制帧已发送，CAN ID=0x18EF2010\n");
}

void iThruster_HEU::servoControlFrame()
{
	// 舵机1 (左上舵机A) - 节点号1
	//sendServoAngle(1, dRudderA);

	// 舵机2 (右上舵机B) - 节点号2
	//sendServoAngle(2, dRudderB);

	// 舵机3 (左下舵机C) - 节点号3
	//sendServoAngle(3, dRudderC);

	// 舵机4 (右下舵机D) - 节点号4
	//sendServoAngle(4, dRudderD);

	// // 测试新增的0xC2命令功能 (使用45度转换后的实际值)
	// 	MOOSTrace("=== 测试0xC2命令 ===\n");
	// 	sendServoAngleByC2(1, dRudderA);  // 使用45度转换后的舵机A实际值
	// 	sendServoAngleByC2(2, dRudderB);  // 使用45度转换后的舵机B实际值
	// 	sendServoAngleByC2(3, dRudderC);  // 使用45度转换后的舵机C实际值
	// 	sendServoAngleByC2(4, dRudderD);  // 使用45度转换后的舵机D实际值
	// 	MOOSTrace("=== 0xC2命令测试完成 ===\n");
	// 测试新增的0x1FFFF300四舵机控制命令
	MOOSTrace("=== 测试0x1FFFF300四舵机控制命令 ===\n");
	fourshSendcontrolmsg();  // 一个报文控制四个舵机
	MOOSTrace("servoControlFrame: 四个舵机控制指令已发送\n");
}

void iThruster_HEU::sendServoAngle(uint8_t servoNode, double angle)
{
	std::vector<uint8_t> Frame(13, 0);

	// 设置数据长度
	Frame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 舵机控制指令 (0x1FFFF3nn)
	Frame[1] = 0x1F;			 // CAN ID字节0
	Frame[2] = 0xFF;			 // CAN ID字节1
	Frame[3] = 0xF3;			 // CAN ID字节2
	Frame[4] = 0x00 | servoNode; // CAN ID字节3 + 节点号

	// 按照舵机协议设置数据部分
	Frame[5] = 0x01;	  // 命令字：角度控制
	Frame[6] = servoNode; // 节点号
	Frame[7] = 0x00;	  // 保留字节

	// 目标角度 (角度×10，int16格式，大端存储)
	int16_t targetAngle = (int16_t)(angle * 10);

	// 大端格式存储角度：高字节在前，低字节在后
	Frame[8] = (uint8_t)((targetAngle >> 8) & 0xFF); // 角度高字节
	Frame[9] = (uint8_t)(targetAngle & 0xFF);		 // 角度低字节

	MOOSTrace("sendServoAngle: 舵机%d 目标角度=%.1f度 (原始值=%d)\n",
			  servoNode, angle, targetAngle);

	// 发送控制帧
	SendFrame(Frame);
}

void iThruster_HEU::sendServoParameterCommand(uint8_t servoNode, uint8_t command, uint16_t data)
{
	std::vector<uint8_t> Frame(13, 0);

	// 设置数据长度 (网转CAN格式)
	Frame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 舵机参数设置 (0x1FFFF3FC)
	Frame[1] = 0x1F; // CAN ID字节0
	Frame[2] = 0xFF; // CAN ID字节1
	Frame[3] = 0xF3; // CAN ID字节2
	Frame[4] = 0xFC; // CAN ID字节3 (参数设置ID)

	// 设置数据部分 (Frame[5-12]为8字节CAN数据)
	Frame[5] = command;   // Byte0: 命令字 (如0xC2)
	Frame[6] = servoNode; // Byte1: 节点号 (1-4)

	// Byte2-3: 数据字段 (大端格式：高字节在前)
	Frame[7] = (uint8_t)((data >> 8) & 0xFF); // 数据高字节
	Frame[8] = (uint8_t)(data & 0xFF);        // 数据低字节

	// Byte4-7: 剩余字节补0
	Frame[9] = 0x00;
	Frame[10] = 0x00;
	Frame[11] = 0x00;
	Frame[12] = 0x00;

	MOOSTrace("sendServoParameterCommand: 舵机%d 命令=0x%02X 数据=%d (网转CAN格式)\n",
			  servoNode, command, data);

	// 发送控制帧
	SendFrame(Frame);
}

void iThruster_HEU::sendServoAngleByC2(uint8_t servoNode, double angle)
{
	// 角度×10转换为uint16
	uint16_t angleData = (uint16_t)(angle * 10);

	// 发送0xC2角度设置命令
	sendServoParameterCommand(servoNode, 0xC2, angleData);

	MOOSTrace("sendServoAngleByC2: 舵机%d 使用0xC2命令设置角度=%.1f度\n",
			  servoNode, angle);
}

void iThruster_HEU::fourshSendcontrolmsg()
{
	//现在新增加一个命令控制四个舵机的命令 - 0x1FFFF300协议
	vector<uint8_t> Frame(13, 0);

	// 设置数据长度
	Frame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 四舵机控制指令 (0x1FFFF300)
	Frame[1] = 0x1F; // CAN ID字节0
	Frame[2] = 0xFF; // CAN ID字节1
	Frame[3] = 0xF3; // CAN ID字节2
	Frame[4] = 0x00; // CAN ID字节3

	// 将四个舵机角度转换为int16格式 (角度×10)
	int16_t angle1 = (int16_t)(dRudderA * 10); // 舵机1角度
	int16_t angle2 = (int16_t)(dRudderB * 10); // 舵机2角度
	int16_t angle3 = (int16_t)(dRudderC * 10); // 舵机3角度
	int16_t angle4 = (int16_t)(dRudderD * 10); // 舵机4角度

	// Byte0-1: 舵机1角度 (大端格式：高字节在前)
	Frame[5] = (uint8_t)((angle1 >> 8) & 0xFF); // 舵机1角度高字节
	Frame[6] = (uint8_t)(angle1 & 0xFF);        // 舵机1角度低字节

	// Byte2-3: 舵机2角度 (大端格式：高字节在前)
	Frame[7] = (uint8_t)((angle2 >> 8) & 0xFF); // 舵机2角度高字节
	Frame[8] = (uint8_t)(angle2 & 0xFF);        // 舵机2角度低字节

	// Byte4-5: 舵机3角度 (大端格式：高字节在前)
	Frame[9] = (uint8_t)((angle3 >> 8) & 0xFF);  // 舵机3角度高字节
	Frame[10] = (uint8_t)(angle3 & 0xFF);        // 舵机3角度低字节

	// Byte6-7: 舵机4角度 (大端格式：高字节在前)
	Frame[11] = (uint8_t)((angle4 >> 8) & 0xFF); // 舵机4角度高字节
	Frame[12] = (uint8_t)(angle4 & 0xFF);        // 舵机4角度低字节

	MOOSTrace("fourshSendcontrolmsg: 四舵机控制 - A=%.1f°, B=%.1f°, C=%.1f°, D=%.1f°\n",
			  dRudderA, dRudderB, dRudderC, dRudderD);
	MOOSTrace("fourshSendcontrolmsg: 角度数据 - %d, %d, %d, %d\n",
			  angle1, angle2, angle3, angle4);

	// 发送控制帧
	SendFrame(Frame);
	MOOSTrace("fourshSendcontrolmsg: 0x1FFFF300四舵机控制帧已发送\n");
}

void iThruster_HEU::SetEnable()
{
	//这个是预充和使能的按钮操作，当解析到命令格式为MsgType=control;Act=function;Type=elevator;Enable=yes/no;
	// 那么就执行预充和使能的操作
	// 格式是先预充命令为 01 00 00 00 00 00 00 00
	//在发送使能的命令04 00 00 00 00 00 00 00
	//当检测到这两个命令之后在进行发送推进器的命令就可以了

	MOOSTrace("SetEnable: 开始执行预充和使能操作\n");

	// 第一步：发送预充命令
	std::vector<uint8_t> PrechargeFrame(13, 0);
	PrechargeFrame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 推进器控制指令 (0x18EF2010)
	PrechargeFrame[1] = 0x18; // CAN ID字节0
	PrechargeFrame[2] = 0xEF; // CAN ID字节1
	PrechargeFrame[3] = 0x20; // CAN ID字节2
	PrechargeFrame[4] = 0x10; // CAN ID字节3

	// 预充命令数据: 01 00 00 00 00 00 00 00
	PrechargeFrame[5] = 0x01; // Byte0: 预充命令
	PrechargeFrame[6] = 0x00; // Byte1
	PrechargeFrame[7] = 0x00; // Byte2
	PrechargeFrame[8] = 0x00; // Byte3
	PrechargeFrame[9] = 0x00;  // Byte4
	PrechargeFrame[10] = 0x00; // Byte5
	PrechargeFrame[11] = 0x00; // Byte6
	PrechargeFrame[12] = 0x00; // Byte7

	MOOSTrace("SetEnable: 发送预充命令\n");
	SendFrame(PrechargeFrame);

	// 延迟100ms
	usleep(100000); // 100ms = 100,000微秒

	// 第二步：发送使能命令
	std::vector<uint8_t> EnableFrame(13, 0);
	EnableFrame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 推进器控制指令 (0x18EF2010)
	EnableFrame[1] = 0x18; // CAN ID字节0
	EnableFrame[2] = 0xEF; // CAN ID字节1
	EnableFrame[3] = 0x20; // CAN ID字节2
	EnableFrame[4] = 0x10; // CAN ID字节3

	// 使能命令数据: 04 00 00 00 00 00 00 00
	EnableFrame[5] = 0x04; // Byte0: 使能命令
	EnableFrame[6] = 0x00; // Byte1
	EnableFrame[7] = 0x00; // Byte2
	EnableFrame[8] = 0x00; // Byte3
	EnableFrame[9] = 0x00;  // Byte4
	EnableFrame[10] = 0x00; // Byte5
	EnableFrame[11] = 0x00; // Byte6
	EnableFrame[12] = 0x00; // Byte7

	MOOSTrace("SetEnable: 发送使能命令\n");
	SendFrame(EnableFrame);

	MOOSTrace("SetEnable: 预充和使能操作完成，推进器已准备就绪\n");
}

void iThruster_HEU::SetDisable()
{
	//这个是安全关闭序列，当Enable=no时执行
	//1. 发送推进器转速为0的报文
	//2. 发送清除使能命令 (预充=1, 使能=0)
	//3. 发送清除预充命令 (预充=0, 使能=0)

	MOOSTrace("SetDisable: 开始执行安全关闭序列\n");

	// 第一步：发送推进器转速为0的报文
	std::vector<uint8_t> StopFrame(13, 0);
	StopFrame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 推进器控制指令 (0x18EF2010)
	StopFrame[1] = 0x18; // CAN ID字节0
	StopFrame[2] = 0xEF; // CAN ID字节1
	StopFrame[3] = 0x20; // CAN ID字节2
	StopFrame[4] = 0x10; // CAN ID字节3

	// 推进器停止命令数据 (转速=0, 但保持预充和使能)
	StopFrame[5] = 0x0A; // Byte0: 预充=1, 使能=1
	StopFrame[6] = 0x01; // Byte1: 转速模式
	StopFrame[7] = 0x00; // Byte2: 扭矩百分比高字节
	StopFrame[8] = 0x00; // Byte3: 扭矩百分比低字节
	StopFrame[9] = 0x00;  // Byte4: 保留
	StopFrame[10] = 0x00; // Byte5: 保留
	StopFrame[11] = 0x00; // Byte6: 转速低字节 (0 RPM)
	StopFrame[12] = 0x00; // Byte7: 转速高字节 (0 RPM)

	MOOSTrace("SetDisable: 发送推进器停止命令 (转速=0)\n");
	SendFrame(StopFrame);

	// 延迟100ms
	usleep(100000);

	// 第二步：发送清除使能命令 (预充=1, 使能=0)
	std::vector<uint8_t> DisableFrame(13, 0);
	DisableFrame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 推进器控制指令 (0x18EF2010)
	DisableFrame[1] = 0x18; // CAN ID字节0
	DisableFrame[2] = 0xEF; // CAN ID字节1
	DisableFrame[3] = 0x20; // CAN ID字节2
	DisableFrame[4] = 0x10; // CAN ID字节3

	// 清除使能命令数据: 02 00 00 00 00 00 00 00 (预充=1, 使能=0)
	DisableFrame[5] = 0x02; // Byte0: 预充=1(Bit1), 使能=0(Bit3)
	DisableFrame[6] = 0x00; // Byte1-7: 全部为0
	DisableFrame[7] = 0x00;
	DisableFrame[8] = 0x00;
	DisableFrame[9] = 0x00;
	DisableFrame[10] = 0x00;
	DisableFrame[11] = 0x00;
	DisableFrame[12] = 0x00;

	MOOSTrace("SetDisable: 发送清除使能命令\n");
	SendFrame(DisableFrame);

	// 延迟100ms
	usleep(100000);

	// 第三步：发送清除预充命令 (预充=0, 使能=0)
	std::vector<uint8_t> PrechargeOffFrame(13, 0);
	PrechargeOffFrame[0] = 0x08; // 8字节数据长度

	// 设置CAN ID - 推进器控制指令 (0x18EF2010)
	PrechargeOffFrame[1] = 0x18; // CAN ID字节0
	PrechargeOffFrame[2] = 0xEF; // CAN ID字节1
	PrechargeOffFrame[3] = 0x20; // CAN ID字节2
	PrechargeOffFrame[4] = 0x10; // CAN ID字节3

	// 清除预充命令数据: 00 00 00 00 00 00 00 00 (预充=0, 使能=0)
	PrechargeOffFrame[5] = 0x00; // Byte0-7: 全部为0
	PrechargeOffFrame[6] = 0x00;
	PrechargeOffFrame[7] = 0x00;
	PrechargeOffFrame[8] = 0x00;
	PrechargeOffFrame[9] = 0x00;
	PrechargeOffFrame[10] = 0x00;
	PrechargeOffFrame[11] = 0x00;
	PrechargeOffFrame[12] = 0x00;

	MOOSTrace("SetDisable: 发送清除预充命令\n");
	SendFrame(PrechargeOffFrame);

	MOOSTrace("SetDisable: 安全关闭序列完成，推进器已完全关闭\n");
}
bool iThruster_HEU::TypeChoice(std::string param)
{
	// 输入格式: Type=debug;mode=manual;desired_percent=□;rud=□;wing=□;time=□;
	// 主推DESIRED_PERCENT,垂直舵RUD、水平舵WING、工作时间TIME
	MOOSTrace("=== TypeChoice 开始解析命令 ===\n");
	MOOSTrace("输入命令: [%s]\n", param.c_str());
	// 这是副本
	std::string sStr = param;
	// 转换为大写
	MOOSToUpper(sStr);
	// 去除字符串两端的空格
	MOOSTrimWhiteSpace(sStr);

	// 解析新的命令格式: TYPE=DEBUG;MODE=MANUAL;DESIRED_PERCENT=□;RUD=□;WING=□;TIME=□;
	std::string sOneParam;
	std::string sParamName;
	std::string sParamVal;

	// 第一步：解析第一个参数（可能是TYPE或MSGTYPE）
	sOneParam = MOOSChomp(sStr, ";");
	sParamName = MOOSChomp(sOneParam, "=");
	sParamVal = sOneParam;

	if (sParamName == "MSGTYPE")
	{
		if (sParamVal == "CONTROL")
		{
			// 第二步：解析Act
			sOneParam = MOOSChomp(sStr, ";");
			sParamName = MOOSChomp(sOneParam, "=");
			sParamVal = sOneParam;

			if (sParamName == "ACT")
			{
				if (sParamVal == "FUNCTION")
				{
					// 第三步：解析Type
					sOneParam = MOOSChomp(sStr, ";");
					sParamName = MOOSChomp(sOneParam, "=");
					sParamVal = sOneParam;

					if (sParamName == "TYPE")
					{
						if (sParamVal == "ELEVATOR")
						{
							// 第四步：解析Enable
							sOneParam = MOOSChomp(sStr, ";");
							sParamName = MOOSChomp(sOneParam, "=");
							sParamVal = sOneParam;

							if (sParamName == "ENABLE")
							{
								if (sParamVal == "YES")
								{
									MOOSTrace("解析到Enable=yes，执行预充使能操作\n");
									SetEnable();
									MOOSTrace("=== 预充使能命令执行完成 ===\n");
									return true;
								}
								else if (sParamVal == "NO")
								{
									MOOSTrace("解析到Enable=no，执行安全关闭序列\n");
									SetDisable();
									MOOSTrace("=== 安全关闭命令执行完成 ===\n");
									return true;
								}
								else
								{
									MOOSTrace("错误：ENABLE值应为YES或NO，当前值为: %s\n", sParamVal.c_str());
									return false;
								}
							}
							else
							{
								MOOSTrace("错误：第四个参数应为ENABLE，当前参数为: %s\n", sParamName.c_str());
								return false;
							}
						}
						else
						{
							MOOSTrace("错误：TYPE值应为ELEVATOR，当前值为: %s\n", sParamVal.c_str());
							return false;
						}
					}
					else
					{
						MOOSTrace("错误：第三个参数应为TYPE，当前参数为: %s\n", sParamName.c_str());
						return false;
					}
				}
				else
				{
					MOOSTrace("错误：ACT值应为FUNCTION，当前值为: %s\n", sParamVal.c_str());
					return false;
				}
			}
			else
			{
				MOOSTrace("错误：第二个参数应为ACT，当前参数为: %s\n", sParamName.c_str());
				return false;
			}
		}
		else
		{
			MOOSTrace("错误：MSGTYPE值应为CONTROL，当前值为: %s\n", sParamVal.c_str());
			return false;
		}
	}
	else if (sParamName == "TYPE")
	{
		if (sParamVal == "DEBUG")
		{
			// 第二步：解析mode
			sOneParam = MOOSChomp(sStr, ";");
			sParamName = MOOSChomp(sOneParam, "=");
			sParamVal = sOneParam;

			if (sParamName == "MODE")
			{
				if (sParamVal == "MANUAL")
				{
					// 继续解析控制参数
					// 解析控制参数
					double desired_percent = 0.0;
					double rud_angle = 0.0;
					double wing_angle = 0.0;
					double work_time = 0.0;

					bool has_desired_percent = false;
					bool has_rud = false;
					bool has_wing = false;
					bool has_time = false;

					// 循环解析剩余参数
					while (!sStr.empty())
					{
						sOneParam = MOOSChomp(sStr, ";");
						if (sOneParam.empty())
							break;

						sParamName = MOOSChomp(sOneParam, "=");
						sParamVal = sOneParam;

						if (sParamName == "DESIRED_PERCENT")
						{
							desired_percent = atof(sParamVal.c_str());
							has_desired_percent = true;
							MOOSTrace("解析到主推转速: %f\n", desired_percent);
						}
						else if (sParamName == "RUD")
						{
							rud_angle = atof(sParamVal.c_str());
							has_rud = true;
							MOOSTrace("解析到垂直舵角度: %f\n", rud_angle);
						}
						else if (sParamName == "WING")
						{
							wing_angle = atof(sParamVal.c_str());
							has_wing = true;
							MOOSTrace("解析到水平舵角度: %f\n", wing_angle);
						}
						else if (sParamName == "TIME")
						{
							work_time = atof(sParamVal.c_str());
							has_time = true;
							MOOSTrace("解析到工作时间: %f\n", work_time);
						}
					}

					// 应用解析到的控制参数
					if (has_desired_percent)
					{
						// 推进器控制：直接设置转速值，不需要标志位
						dT1 = desired_percent;
						MOOSTrace("TypeChoice: 设置主推转速为 %f RPM\n", dT1);
					}

					if (has_rud)
					{
						// 舵机控制：RUD参数控制垂直舵（俯仰控制），直接设置角度值
						// 不需要标志位，每次Iterate都会重新计算45度转换并发送
						dRudderUp = rud_angle;
						dRudderDown = rud_angle;
						MOOSTrace("TypeChoice: 设置垂直舵角度为 %f (Up=%f, Down=%f)\n",
								  rud_angle, dRudderUp, dRudderDown); 
					}

					if (has_wing)
					{
						// 舵机控制：WING参数控制水平舵（偏航控制），直接设置角度值
						dRudderLeft = wing_angle;
						dRudderRight = wing_angle;
						MOOSTrace("TypeChoice: 设置水平舵角度为 %f (Left=%f, Right=%f)\n",
								  wing_angle, dRudderLeft, dRudderRight);
					}

					if (has_time)
					{
						MOOSTrace("TypeChoice: 工作时间设置为 %f 秒\n", work_time);
						// TODO: 实现工作时间控制逻辑
					}

					MOOSTrace("=== TypeChoice 解析成功 ===\n");
					return true;
				}
				else
				{
					MOOSTrace("错误：MODE值应为MANUAL，当前值为: %s\n", sParamVal.c_str());
					return false;
				}
			}
			else
			{
				MOOSTrace("错误：第二个参数应为MODE，当前参数为: %s\n", sParamName.c_str());
				return false;
			}
		}
		else
		{
			MOOSTrace("错误：TYPE值应为DEBUG，当前值为: %s\n", sParamVal.c_str());
			return false;
		}
	}
	else
	{
		MOOSTrace("错误：命令格式错误，第一个参数应为TYPE，当前参数为: %s\n", sParamName.c_str());
		return false;
	}

	// 如果执行到这里，说明命令格式不正确
	MOOSTrace("=== TypeChoice 解析失败：命令格式不正确 ===\n");
	return false;
}