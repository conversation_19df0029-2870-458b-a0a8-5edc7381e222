ProcessConfig = iThruster_HEU //Net--->can1
{
    AppTick   =10
    CommsTick =10

    // 接收数据的IP地址，UDP套接字绑定的本地IP地址（虚拟机的IP）
    // 使用虚拟机ens33网卡的实际IP地址（从ifconfig获得）
// 虚拟机IP地址，用于接收数据
    RecvIP = ************

    // 接收数据的端口号，对应CAN盒子的目标端口1
    RecvPort = 8001

    // CAN盒子的IP地址
    // 用于发送数据到CAN盒子
    DestIP = *************

    // 发送数据的目标端口号（CAN盒子的工作端口）
    DestPort = 4001

    // 网络连接重试配置
    // 连接失败时的最大重试次数
    MaxRetries = 5

    // 重试之间的延迟时间（秒）
    RetryDelay = 2
}
