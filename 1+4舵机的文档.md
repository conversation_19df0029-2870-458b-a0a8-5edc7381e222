# 推进器+舵机CAN通信协议实施文档

## 1. 系统概述

### 1.1 系统组成
- **推进器数量**：1个（主驱动电机控制器）
- **舵机数量**：4个
- **通信方式**：CAN总线
- **波特率**：250Kbps
- **帧格式**：扩展帧（29位标识符）

### 1.2 系统功能
本系统用于控制水下机器人或船舶的推进和转向：
- 推进器负责提供前进/后退动力
- 4个舵机负责方向控制和姿态调整

## 2. 推进器（主驱动电机）通信协议

### 2.1 推进器CAN ID定义
根据文档，单电机系统的ID如下：

| 报文名称 | CAN ID | 方向 | 发送周期 | 说明 |
|---------|--------|------|----------|------|
| VCU1 | 0x18EF2010 | 整车→电机 | 10ms | 控制指令帧 |
| VCU2 | 0x18EF2110 | 整车→电机 | 100ms | 限制参数帧 |
| MCU1 | 0x18EF1020 | 电机→整车 | 10ms | 状态反馈帧1 |
| MCU2 | 0x18EF1120 | 电机→整车 | 100ms | 状态反馈帧2 |
| MCU3 | 0x18EF1220 | 电机→整车 | 100ms | 状态反馈帧3 |
| MCU4 | 0x18EF1320 | 电机→整车 | 100ms | 状态反馈帧4 |

### 2.2 推进器控制报文详解

#### 2.2.1 控制指令帧 VCU1 (0x18EF2010) - 发送给推进器
**作用**：控制推进器的启停、模式和期望扭矩/转速

| 字节 | 位 | 参数名称 | 值定义 | 说明 |
|------|-----|----------|--------|------|
| Byte1 | Bit1 | 预充状态 | 0=无效<br>1=有效 | 必须置1才能工作 |
| Byte1 | Bit3 | 使能命令 | 0=无效<br>1=有效 | 必须置1才能工作 |
| Byte2 | 全字节 | 动作命令 | 0=扭矩模式<br>1=转速模式<br>2=发电模式<br>3=驱动模式<br>4=驻坡模式<br>5=同步模式<br>6=主动放电<br>15=停机 | 推进器通常使用扭矩或转速模式 |
| Byte3-4 | - | 期望扭矩百分比 | 有符号数<br>精度：4096<br>范围：-1~1 | 扭矩模式时使用<br>实际值=原始值/4096 |
| Byte7-8 | - | 期望转速 | 有符号数<br>单位：rpm | 转速模式时使用 |

**使用示例**：
```
扭矩模式正转50%功率：
Byte1 = 0x0A (Bit1=1, Bit3=1)
Byte2 = 0x00 (扭矩模式)
Byte3 = 0x00
Byte4 = 0x08 (2048/4096 = 50%)
Byte5-6 = 0x00
Byte7-8 = 0x00
```

#### 2.2.2 限制参数帧 VCU2 (0x18EF2110) - 发送给推进器
**作用**：设置推进器的电流限制

| 字节 | 参数名称 | 单位 | 说明 |
|------|----------|------|------|
| Byte1-2 | 电机回馈发电母线限流 | A | 无符号数，限制发电电流 |
| Byte3-4 | 电机驱动可用母线限流 | A | 无符号数，限制驱动电流 |
| Byte5-8 | 保留 | - | 填充0x00 |

### 2.3 推进器反馈报文详解

#### 2.3.1 状态反馈帧1 MCU1 (0x18EF1020) - 推进器反馈
**作用**：高频反馈电流和转速信息（10ms周期）

| 字节 | 参数名称 | 单位 | 数据类型 | 说明 |
|------|----------|------|----------|------|
| Byte1-2 | 电机母线电流 | A | 有符号 | 实时电流值 |
| Byte3-6 | 保留 | - | - | - |
| Byte7-8 | 电机转速 | rpm | 有符号 | 实时转速值 |

#### 2.3.2 状态反馈帧2 MCU2 (0x18EF1120) - 推进器反馈
**作用**：反馈电压、温度和漏水检测（100ms周期）

| 字节 | 参数名称 | 单位 | 数据类型 | 说明 |
|------|----------|------|----------|------|
| Byte1-2 | 电机母线电压 | 0.1V | 无符号 | 实际电压=值×0.1 |
| Byte3-5 | 保留 | - | - | - |
| Byte6 | 漏水检测 | - | 无符号 | 0=正常<br>1=漏水故障 |
| Byte7 | 控制器温度 | ℃ | 无符号 | 实际温度=值-50 |
| Byte8 | 电机温度 | ℃ | 无符号 | 实际温度=值-50 |

#### 2.3.3 状态反馈帧3 MCU3 (0x18EF1220) - 推进器反馈
**作用**：反馈扭矩信息（100ms周期）

| 字节 | 参数名称 | 单位 | 数据类型 | 说明 |
|------|----------|------|----------|------|
| Byte1-2 | 最大扭矩 | Nm | 无符号 | 当前转速下的最大扭矩 |
| Byte3-4 | 保留 | - | - | - |
| Byte5-6 | 当前扭矩 | Nm | 有符号 | 实时输出扭矩 |
| Byte7-8 | 保留 | - | - | - |

#### 2.3.4 状态反馈帧4 MCU4 (0x18EF1320) - 推进器反馈
**作用**：反馈故障信息（100ms周期）

重要故障位定义：
- Byte1-2, Byte5-6, Byte7-8：各种故障标志位（1=故障，0=正常）
- Byte7 Bit8-11：故障等级（0=无故障，1=一级告警，2=二级限扭矩，3=三级停机）

主要故障类型包括：
- 功率模块故障：关管处理
- 相电流传感器故障：关管处理  
- 电机堵转：限转矩50%
- 过流1(550A)：零转矩
- 过流2(600A)：关管
- 过压1(450V)：线性限转矩
- 过压2(500V)：零转矩
- 过压3(550V)：关管
- 欠压1(250V)：线性限转矩
- 欠压2(200V)：零转矩
- 欠压3(150V)：零转矩
- 超速1(6000rpm)：零转矩
- 超速2(8000rpm)：零转矩
- 电机过温1(145℃)：线性限转矩
- 电机过温2(155℃)：零转矩
- 电机过温3(165℃)：关管
- 控制器过温1(75℃)：线性限转矩
- 控制器过温2(85℃)：零转矩
- 控制器过温3(95℃)：关管

## 3. 舵机通信协议

### 3.1 舵机CAN ID定义

假设4个舵机的节点号分别为1、2、3、4，则各舵机的CAN ID如下：

| 舵机编号 | 节点号 | 控制指令ID | 自动反馈ID | 参数设置ID |
|---------|--------|------------|------------|------------|
| 舵机1 | 0x01 | 0x1FFFF301 | 0x1FFFF381 | 0x1FFFF3FC |
| 舵机2 | 0x02 | 0x1FFFF302 | 0x1FFFF382 | 0x1FFFF3FC |
| 舵机3 | 0x03 | 0x1FFFF303 | 0x1FFFF383 | 0x1FFFF3FC |
| 舵机4 | 0x04 | 0x1FFFF304 | 0x1FFFF384 | 0x1FFFF3FC |

### 3.2 舵机控制报文详解

#### 3.2.1 角度控制指令 (0x1FFFF3nn) - 发送给舵机
**数据长度**：5字节
**作用**：控制舵机转到指定角度

| 字节 | 参数名称 | 值 | 说明 |
|------|----------|-----|------|
| Byte0 | 命令字 | 0x01 | 固定为0x01 |
| Byte1 | 节点号 | 1-4 | 目标舵机编号 |
| Byte2 | 保留 | 0x00 | 固定为0 |
| Byte3-4 | 目标角度 | int16 | 实际角度×10<br>如90°=900 |

**使用示例**：
```
控制舵机1转到45度：
Byte0 = 0x01
Byte1 = 0x01 (舵机1)
Byte2 = 0x00
Byte3 = 0x01 (高字节)
Byte4 = 0xC2 (低字节) 
// 450 = 0x01C2
```

#### 3.2.2 舵机自动反馈 (0x1FFFF3nn|0x80) - 舵机反馈
**数据长度**：8字节
**作用**：舵机自动反馈当前状态

| 字节 | 参数名称 | 数据类型 | 说明 |
|------|----------|----------|------|
| Byte0-1 | 当前角度 | int16 | 实际角度×10 |
| Byte2-3 | 当前电流 | uint16 | 实际电流×10，单位0.1A |
| Byte4-5 | 当前温度 | int16 | 实际温度×10，单位0.1℃ |
| Byte6-7 | 故障码 | uint16 | 见故障码定义表 |

**故障码位定义**（0=异常，1=正常）：

| 位 | 故障类型 | 说明 |
|----|----------|------|
| Bit0 | 过流 | 电机电流超限 |
| Bit1 | 堵转 | 电机堵转 |
| Bit2 | 温度 | 温度异常 |
| Bit3 | 硬件短路 | 硬件故障 |
| Bit4 | 时间 | 超时故障 |
| Bit5 | 角度超限 | 超出角度范围 |
| Bit6 | 漏水 | 检测到漏水 |
| Bit7 | 漏油 | 检测到漏油 |
| Bit8 | 过压 | 电压过高 |
| Bit9 | 欠压 | 电压过低 |
| Bit10 | ADS传感器异常 | 传感器故障 |
| Bit11 | 角度传感器异常 | 编码器故障 |
| Bit12 | CANID异常 | 通信故障 |
| Bit13 | 预充 | 预充电异常 |

### 3.3 舵机参数设置

#### 3.3.1 常用参数设置命令 (0x1FFFF3FC)

| 命令字 | 功能 | 数据格式 | 说明 |
|--------|------|----------|------|
| 0xC1 | 读取序列号 | Byte1=节点号 | 返回4字节序列号 |
| 0xC2 | 设置舵机角度 | Byte1=节点号<br>Byte2-3=角度×10 | 角度控制命令 |
| 0xD0 | 设置节点号 | Byte1=当前节点号<br>Byte2=新节点号 | 修改舵机地址 |
| 0xD1 | 设置零位 | Byte1=节点号 | 将当前位置设为0度 |
| 0xD2 | 设置反馈频率 | Byte1=节点号<br>Byte2=频率(Hz) | 设置自动反馈频率，0=关闭 |
| 0xD7 | 设置波特率 | Byte1=节点号<br>Byte2=1-7 | 1=10K, 2=50K, 3=100K, 4=125K, 5=250K, 6=500K, 7=1M |

#### 3.3.2 参数查询命令 (0x1FFFF3FC)

| 命令字 | 功能 | 返回数据 | 说明 |
|--------|------|----------|------|
| 0xE0 | 读取节点号 | Byte0=节点号 | 查询当前节点号 |
| 0xE1 | 读取零位霍尔电压 | Byte1-2=电压×1000 | 查询零位时的霍尔电压 |
| 0xE2 | 读取反馈频率 | Byte1=频率 | 查询当前反馈频率 |
| 0xE6 | 读取运行时间 | Byte1-4=运行时间 | 查询累计运行时间 |
| 0xE7 | 读取波特率 | Byte1=波特率代码 | 查询当前波特率设置 |

## 4. 实际应用示例

### 4.1 系统初始化流程
```
1. 推进器初始化
   - 发送VCU2设置电流限制（如驱动300A，回馈100A）
   - 发送VCU1使能命令（预充=1，使能=1，模式=停机）
   
2. 舵机初始化（对每个舵机）
   - 发送0xD2命令设置反馈频率（如10Hz）
   - 发送角度控制命令回中（0度）
```

### 4.2 正常运行控制
```
1. 推进器控制（10ms周期）
   - 发送VCU1控制扭矩或转速
   - 接收MCU1获取电流和转速反馈
   
2. 舵机控制（按需发送）
   - 发送角度控制命令
   - 接收自动反馈监控状态
   
3. 状态监控（100ms周期）
   - 检查推进器MCU2的漏水状态
   - 检查推进器MCU4的故障等级
   - 检查各舵机反馈的故障码
```

### 4.3 故障处理
```
1. 推进器故障
   - 一级故障：记录日志，继续运行
   - 二级故障：降低功率运行
   - 三级故障：立即停机
   
2. 舵机故障
   - 检查故障码各位
   - 漏水/过温：停止该舵机
   - 堵转：尝试反向运动解除
```

## 5. 注意事项

1. **通信时序**：推进器必须先发送预充和使能信号才能响应控制命令
2. **数据格式**：注意大小端问题，协议使用Intel格式（小端）
3. **故障处理**：定期检查故障反馈，及时处理异常
4. **温度监控**：推进器和舵机都有温度反馈，注意散热
5. **漏水检测**：这是关键安全功能，必须实时监控
6. **驻坡模式**：VCU需要发送0x18EF6010第2个字节，低位4位发0x04驻坡模式，目标转速发0
7. **VCU控制**：需要发18EFX010第一字节，"开管指令""预充信号"都使能，电机控制才能响应控制指令
8. **电机故障等级**：一级故障报警，二级故障限制功率，三级故障停车

## 6. 编程建议

1. **结构体定义**：为每种报文定义对应的结构体
2. **周期发送**：使用定时器确保控制命令的发送周期
3. **数据转换**：注意单位转换（如角度×10，温度-50等）
4. **错误处理**：建立完善的错误处理机制
5. **日志记录**：记录所有故障和异常情况

## 7. MOOS-IvP 实现规划

### 7.1 系统架构设计

#### 7.1.1 MOOS应用程序架构
```
pThrusterServoInterface/
├── ThrusterServoInterface.h      # 主类头文件
├── ThrusterServoInterface.cpp    # 主类实现
├── ThrusterServoInterface_Info.h # MOOS应用信息
├── main.cpp                      # 程序入口
├── CANInterface.h                # CAN通信接口类
├── CANInterface.cpp              # CAN通信实现
├── ThrusterControl.h             # 推进器控制类
├── ThrusterControl.cpp           # 推进器控制实现
├── ServoControl.h                # 舵机控制类
├── ServoControl.cpp              # 舵机控制实现
└── CMakeLists.txt               # CMake配置文件
```

#### 7.1.2 类设计
- **ThrusterServoInterface**: 继承自CMOOSApp，主控制类
- **CANInterface**: 封装SocketCAN操作
- **ThrusterControl**: 推进器控制逻辑
- **ServoControl**: 舵机控制逻辑

### 7.2 MOOS变量定义

#### 7.2.1 输入变量（订阅）
| MOOS变量名 | 类型 | 范围 | 说明 |
|-----------|------|------|------|
| DESIRED_THRUST | double | -100~100 | 期望推力百分比 |
| DESIRED_SPEED | double | -3000~3000 | 期望转速(rpm) |
| THRUST_MODE | string | "TORQUE"/"SPEED" | 推进器控制模式 |
| SERVO1_ANGLE | double | -90~90 | 舵机1目标角度 |
| SERVO2_ANGLE | double | -90~90 | 舵机2目标角度 |
| SERVO3_ANGLE | double | -90~90 | 舵机3目标角度 |
| SERVO4_ANGLE | double | -90~90 | 舵机4目标角度 |
| SYSTEM_ENABLE | string | "TRUE"/"FALSE" | 系统使能 |
| EMERGENCY_STOP | string | "TRUE"/"FALSE" | 紧急停止 |

#### 7.2.2 输出变量（发布）
| MOOS变量名 | 类型 | 更新频率 | 说明 |
|-----------|------|----------|------|
| THRUSTER_RPM | double | 100Hz | 推进器实际转速 |
| THRUSTER_CURRENT | double | 100Hz | 推进器电流 |
| THRUSTER_TORQUE | double | 10Hz | 推进器扭矩 |
| THRUSTER_TEMP | double | 10Hz | 推进器温度 |
| THRUSTER_VOLTAGE | double | 10Hz | 母线电压 |
| THRUSTER_STATUS | string | 10Hz | 推进器状态字符串 |
| THRUSTER_FAULT | string | 变化时 | 故障信息 |
| SERVO1_ACTUAL | double | 10Hz | 舵机1实际角度 |
| SERVO2_ACTUAL | double | 10Hz | 舵机2实际角度 |
| SERVO3_ACTUAL | double | 10Hz | 舵机3实际角度 |
| SERVO4_ACTUAL | double | 10Hz | 舵机4实际角度 |
| SERVO1_STATUS | string | 10Hz | 舵机1状态 |
| SERVO2_STATUS | string | 10Hz | 舵机2状态 |
| SERVO3_STATUS | string | 10Hz | 舵机3状态 |
| SERVO4_STATUS | string | 10Hz | 舵机4状态 |
| LEAK_DETECTED | string | 变化时 | 漏水检测 |

### 7.3 配置文件设计

#### 7.3.1 pThrusterServoInterface.moos配置
```
ProcessConfig = pThrusterServoInterface
{
  AppTick   = 20    // 20Hz主循环
  CommsTick = 20    // 20Hz MOOS通信
  
  // CAN接口配置
  CAN_INTERFACE = can0
  CAN_BAUDRATE = 250000
  
  // 推进器配置
  THRUSTER_ID = 0x20           // 推进器节点地址
  THRUSTER_MAX_RPM = 3000      // 最大转速限制
  THRUSTER_MAX_CURRENT = 300   // 最大电流限制(A)
  THRUSTER_REGEN_CURRENT = 100 // 回馈电流限制(A)
  
  // 舵机配置
  SERVO1_NODE = 1              // 舵机1节点号
  SERVO2_NODE = 2              // 舵机2节点号
  SERVO3_NODE = 3              // 舵机3节点号
  SERVO4_NODE = 4              // 舵机4节点号
  SERVO_ANGLE_LIMIT = 90       // 舵机角度限制(度)
  SERVO_FEEDBACK_RATE = 10     // 舵机反馈频率(Hz)
  
  // 安全配置
  AUTO_STOP_ON_FAULT = true    // 故障自动停机
  
  // 日志配置
  LOG_LEVEL = INFO             // 日志级别
  LOG_CAN_RAW = false          // 记录原始CAN数据
}
```

