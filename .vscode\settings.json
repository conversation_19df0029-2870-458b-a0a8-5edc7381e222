{"files.associations": {"algorithm": "cpp", "random": "cpp", "streambuf": "cpp", "cmath": "cpp", "functional": "cpp", "type_traits": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "utility": "cpp", "format": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "span": "cpp", "stdexcept": "cpp", "text_encoding": "cpp", "typeinfo": "cpp", "variant": "cpp"}}