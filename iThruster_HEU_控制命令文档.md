# iThruster_HEU 控制命令格式文档

## 1. MOOS消息控制格式

### 1.1 基本MOOS变量控制
类似于 `uPokeDB CONTROL_MSG="MsgType=control;ACT=PayloadBattery_charge;TYPE=motion;Power=on;"`

#### 推进器控制
```bash
# 推进器转速控制（RPM）
uPokeDB DESIRED_Percent="1500"

# 示例：设置推进器转速为1500 RPM
uPokeDB DESIRED_Percent="1500"
```

#### 舵机控制
```bash
# 统一舵机控制（通过TypeChoice命令）
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=0;rud=30;wing=15;time=0;"

# 示例：设置垂直舵机30度
uPokeDB DESIRED_RUDDER_UP="30.0"
```

### 1.2 TypeChoice命令格式
```bash
# 完整命令格式
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=1500;rud=30;wing=15;time=10;"

# 参数说明：
# Type=debug          - 固定为debug模式
# mode=manual         - 固定为manual模式
# desired_percent=□   - 推进器转速(RPM)
# rud=□              - 垂直舵角度(度)
# wing=□             - 水平舵角度(度)
# time=□             - 工作时间(秒)

# 示例命令：
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=1500;rud=30;wing=15;time=10;"
```

### 1.3 预充使能控制格式
```bash
# 预充使能命令格式
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=yes/no;"

# 参数说明：
# MsgType=control     - 固定为control模式
# Act=function        - 固定为function操作
# Type=elevator       - 固定为elevator类型
# Enable=yes/no       - yes=执行预充使能, no=执行安全关闭

# 示例命令：
# 执行预充使能
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=yes;"

# 执行安全关闭
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=no;"
```

## 2. 底层CAN协议格式

### 2.1 推进器控制帧 (VCU1协议)
```
CAN ID: 0x18EF2010
数据长度: 8字节
格式: [08 18 EF 20 10] [0A 01 00 00 00 00 RPM_L RPM_H]

字节说明：
- Byte0: 0x0A (预充=1, 使能=1)
- Byte1: 0x01 (转速模式)
- Byte2-3: 0x0000 (扭矩百分比，转速模式时为0)
- Byte4-5: 0x0000 (保留)
- Byte6-7: 期望转速(int16, 小端格式)

示例：1500 RPM
08 18 EF 20 10 0A 01 00 00 00 00 DC 05
```

### 2.2 舵机控制帧 (0xC2命令)
```
CAN ID: 0x1FFFF3FC
数据长度: 8字节
格式: [08 1F FF F3 FC] [C2 NODE ANGLE_L ANGLE_H 00 00 00 00]

字节说明：
- Byte0: 0xC2 (角度设置命令)
- Byte1: 节点号 (1-4)
- Byte2-3: 角度×10 (uint16, 小端格式)
- Byte4-7: 0x00000000 (保留)

示例：舵机1设置30度
08 1F FF F3 FC C2 01 2C 01 00 00 00 00
```

### 2.3 四舵机控制帧 (0x1FFFF300命令)
```
CAN ID: 0x1FFFF300
数据长度: 8字节
格式: [08 1F FF F3 00] [ANGLE1_H ANGLE1_L ANGLE2_H ANGLE2_L ANGLE3_H ANGLE3_L ANGLE4_H ANGLE4_L]

字节说明：
- Byte0-1: 舵机1角度×10 (int16, 大端格式)
- Byte2-3: 舵机2角度×10 (int16, 大端格式)
- Byte4-5: 舵机3角度×10 (int16, 大端格式)
- Byte6-7: 舵机4角度×10 (int16, 大端格式)

数据格式详细说明：
- Byte0: 舵机1角度高字节 (angle1H)
- Byte1: 舵机1角度低字节 (angle1L)
- Byte2: 舵机2角度高字节 (angle2H)
- Byte3: 舵机2角度低字节 (angle2L)
- Byte4: 舵机3角度高字节 (angle3H)
- Byte5: 舵机3角度低字节 (angle3L)
- Byte6: 舵机4角度高字节 (angle4H)
- Byte7: 舵机4角度低字节 (angle4L)

示例：四个舵机都设置30度 (30×10=300=0x012C)
08 1F FF F3 00 01 2C 01 2C 01 2C 01 2C
```

### 2.4 预充使能控制帧
```
CAN ID: 0x18EF2010
数据长度: 8字节

预充命令: [08 18 EF 20 10] [01 00 00 00 00 00 00 00]
使能命令: [08 18 EF 20 10] [04 00 00 00 00 00 00 00]
清除使能: [08 18 EF 20 10] [02 00 00 00 00 00 00 00]
清除预充: [08 18 EF 20 10] [00 00 00 00 00 00 00 00]

位定义：
- Bit1: 预充状态 (0=无效, 1=有效) → 值为2
- Bit3: 使能命令 (0=无效, 1=有效) → 值为8
- 预充+使能: 2+8=10=0x0A
- 仅预充: 2=0x02
- 全部清除: 0=0x00
```

## 3. 45度坐标转换系统

### 3.1 输入到输出的转换
```
输入：
- dRudderUp/Down   → thetaPitch = (Up + Down) / 2
- dRudderLeft/Right → thetaYaw = (Left + Right) / 2

输出（45度转换）：
- dRudderA = (thetaYaw + thetaPitch) / √2    # 左上舵机（舵机1）
- dRudderB = (thetaYaw - thetaPitch) / √2    # 右上舵机（舵机2）  
- dRudderC = (-thetaYaw + thetaPitch) / √2   # 左下舵机（舵机3）
- dRudderD = (-thetaYaw - thetaPitch) / √2   # 右下舵机（舵机4）
```

### 3.2 实际示例
```
输入：DESIRED_RUDDER_UP = 30.0
计算：
- thetaPitch = 30.0/2 = 15.0°
- thetaYaw = 0°
- dRudderA = (0 + 15)/√2 = 10.6°  → 106 → 0x006A
- dRudderB = (0 - 15)/√2 = -10.6° → -106 → 0xFF96
- dRudderC = (0 + 15)/√2 = 10.6°  → 106 → 0x006A  
- dRudderD = (0 - 15)/√2 = -10.6° → -106 → 0xFF96

发送的CAN帧：
舵机1: 08 1F FF F3 FC C2 01 6A 00 00 00 00 00
舵机2: 08 1F FF F3 FC C2 02 96 FF 00 00 00 00  
舵机3: 08 1F FF F3 FC C2 03 6A 00 00 00 00 00
舵机4: 08 1F FF F3 FC C2 04 96 FF 00 00 00 00
```

## 4. 常用控制命令示例

### 4.1 推进器控制
```bash
# 前进1500 RPM
uPokeDB DESIRED_Percent="1500"

# 后退1000 RPM  
uPokeDB DESIRED_Percent="-1000"

# 停止
uPokeDB DESIRED_Percent="0"
```

### 4.2 舵机控制
```bash
# 上浮30度
uPokeDB DESIRED_RUDDER_UP="30.0"

# 下潜20度
uPokeDB DESIRED_RUDDER_DOWN="-20.0"

# 左转15度
uPokeDB DESIRED_WING_LEFT="15.0"

# 右转10度  
uPokeDB DESIRED_WING_RIGHT="-10.0"

# 复合动作：上浮30度同时左转15度
uPokeDB DESIRED_RUDDER_UP="30.0"
uPokeDB DESIRED_WING_LEFT="15.0"
```

### 4.3 组合控制命令
```bash
# 完整的机动命令：前进1500RPM，上浮30度，左转15度，持续10秒
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=1500;rud=30;wing=15;time=10;"

# 紧急停止
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=0;rud=0;wing=0;time=0;"

# 悬停状态
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=0;rud=0;wing=0;time=60;"
```

### 4.4 预充使能控制命令
```bash
# 系统启动序列
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=yes;"

# 系统安全关闭序列
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=no;"

# 完整操作流程：
# 1. 先执行预充使能
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=yes;"

# 2. 然后进行正常控制
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=1500;rud=30;wing=15;time=10;"

# 3. 最后安全关闭
uPokeDB CONTROL_MSG="MsgType=control;Act=function;Type=elevator;Enable=no;"
```

### 4.5 四舵机控制说明
```bash
# 四舵机控制是通过TypeChoice命令自动实现的
# 当执行debug命令时，系统会：
# 1. 解析rud和wing参数
# 2. 进行45度坐标系转换
# 3. 自动发送0x1FFFF300四舵机控制帧

# 坐标转换关系：
# dRudderA = (thetaYaw + thetaPitch) / sqrt(2)  # 左上舵机
# dRudderB = (thetaYaw - thetaPitch) / sqrt(2)  # 右上舵机
# dRudderC = (-thetaYaw + thetaPitch) / sqrt(2) # 左下舵机
# dRudderD = (-thetaYaw - thetaPitch) / sqrt(2) # 右下舵机

# 其中：
# thetaYaw = (dRudderLeft + dRudderRight) / 2.0   # 偏航角
# thetaPitch = (dRudderUp + dRudderDown) / 2.0    # 俯仰角

# 示例：上浮30度，左转15度
uPokeDB CONTROL_MSG="Type=debug;mode=manual;desired_percent=1500;rud=30;wing=15;time=10;"
# 系统会自动计算四个舵机的角度并发送0x1FFFF300控制帧
```
```
