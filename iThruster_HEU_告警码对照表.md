# CAN1NET_HEU 告警码对照表

## 系统初始化告警

- CODE=0: MOOS配置文件读取失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=0;LEVEL=FATAL;NOTE=MOOS File Read Failed"`

- CODE=1: 网络绑定失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=1;LEVEL=FATAL;NOTE=Binding Failed"`

- CODE=2: Socket打开失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=2;LEVEL=FATAL;NOTE=Socket Open Failed"`

- CODE=3: 线程创建失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=3;LEVEL=FATAL;NOTE=Thread Create Failed"`

## 网络通信告警

- CODE=4: 帧接收失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=4;LEVEL=WARNING;NOTE=Frame Receive Failed"`

- CODE=5: 帧发送失败
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=5;LEVEL=WARNING;NOTE=Frame Send Failed"`

## 漏水检测告警

- CODE=6: MCU2推进器漏水检测
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=6;LEVEL=FATAL;NOTE=Water Leak Detected in MCU2"`

- CODE=7: 轻微漏水告警
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=7;LEVEL=WARNING;NOTE=Minor Water Leak Detected"`

- CODE=8: 中度漏水告警
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=8;LEVEL=FATAL;NOTE=Moderate Water Leak Detected"`

- CODE=9: 严重漏水告警
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=9;LEVEL=FATAL;NOTE=Severe Water Leak Detected"`

- CODE=10: 致命漏水告警
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=10;LEVEL=FATAL;NOTE=Critical Water Leak Detected"`

- CODE=11: 未知漏水状态告警
  - `AUV_WARN_MSG = "ID=iThruster_HEU;CODE=11;LEVEL=WARNING;NOTE=Unknown Leak Status"`
