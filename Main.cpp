/************************************************************/
/*    NAME: DYF                                              */
/*    ORGN: HEU                                              */
/*    FILE: Main.cpp                                         */
/*    DATE: 2023/11/28                                       */
/************************************************************/

#include "iThruster_HEU.h"
#include "MBUtils.h"

using namespace std;

//----------------------------------------------------------------
// Procedure: showSynopsis

void showSynopsis()
{
	cout << "PROGRAM: iThruster_HEU                                     " << endl;
	cout << "MODULE:  Network to CAN converter for thruster and servo   " << endl;
	cout << "AUTHOR:  DYF, HEU                                          " << endl;
	cout << "DATE:    2023/11/28                                        " << endl;
	cout << "                                                            " << endl;
	cout << "SYNOPSIS:                                                   " << endl;
	cout << "  iThruster_HEU is a MOOS application that converts        " << endl;
	cout << "  network data to CAN protocol for controlling thrusters   " << endl;
	cout << "  and servos. It supports 1+4 servo protocol and provides  " << endl;
	cout << "  leak detection and alarm functionality.                  " << endl;
}

//----------------------------------------------------------------
// Procedure: showHelpAndExit

void showHelpAndExit()
{
	cout << "Usage: iThruster_HEU file.moos [OPTIONS]                    " << endl;
	cout << "                                                            " << endl;
	cout << "Options:                                                    " << endl;
	cout << "  --alias=<ProcessName>                                     " << endl;
	cout << "      Launch iThruster_HEU with the given process name      " << endl;
	cout << "      rather than iThruster_HEU.                            " << endl;
	cout << "  --example, -e                                             " << endl;
	cout << "      Display example MOOS configuration block.            " << endl;
	cout << "  --help, -h                                                " << endl;
	cout << "      Display this help message.                           " << endl;
	cout << "  --interface, -i                                           " << endl;
	cout << "      Display MOOS publications and subscriptions.         " << endl;
	cout << "  --version,-v                                              " << endl;
	cout << "      Display the release version of iThruster_HEU.        " << endl;
	cout << "                                                            " << endl;
	cout << "Note: If argv[2] does not otherwise match a known option,  " << endl;
	cout << "      then it will be interpreted as a run alias. This is  " << endl;
	cout << "      to support pAntler launching conventions.            " << endl;
	cout << "                                                            " << endl;
	exit(0);
}

//----------------------------------------------------------------
// Procedure: showReleaseInfoAndExit

void showReleaseInfoAndExit()
{
	cout << "iThruster_HEU version 2.1.0" << endl;
	cout << "Released under GPL license" << endl;
	exit(0);
}

//----------------------------------------------------------------
// Procedure: showInterfaceAndExit

void showInterfaceAndExit()
{
	showSynopsis();
	showHelpAndExit();
}

//----------------------------------------------------------------
// Procedure: showExampleConfigAndExit

void showExampleConfigAndExit()
{
	cout << "                                                            " << endl;
	cout << "===============================================             " << endl;
	cout << "iThruster_HEU Example MOOS Configuration                   " << endl;
	cout << "===============================================             " << endl;
	cout << "                                                            " << endl;
	cout << "ProcessConfig = iThruster_HEU                               " << endl;
	cout << "{                                                           " << endl;
	cout << "  AppTick   = 10                                            " << endl;
	cout << "  CommsTick = 10                                            " << endl;
	cout << "                                                            " << endl;
	cout << "  RecvIP = \"0.0.0.0\"                                      " << endl;
	cout << "  RecvPort = 40003                                          " << endl;
	cout << "                                                            " << endl;
	cout << "  DestIP = \"************\"                                 " << endl;
	cout << "  DestPort = 40003                                          " << endl;
	cout << "                                                            " << endl;
	cout << "  MaxRetries = 5                                            " << endl;
	cout << "  RetryDelay = 2                                            " << endl;
	cout << "}                                                           " << endl;
	cout << "                                                            " << endl;
	exit(0);
}

int main(int argc, char *argv[])
{
	string sMissionFile = "iThruster_HEU.moos";
	string sAppName = "Main";
	string sMOOSName = "iThruster_HEU";

	for (int i = 1; i < argc; i++)
	{
		string argi = argv[i];
		if ((argi == "-v") || (argi == "--version") || (argi == "-version"))
			showReleaseInfoAndExit();
		else if ((argi == "-e") || (argi == "--example") || (argi == "-example"))
			showExampleConfigAndExit();
		else if ((argi == "-h") || (argi == "--help") || (argi == "-help"))
			showHelpAndExit();
		else if ((argi == "-i") || (argi == "--interface"))
			showInterfaceAndExit();
	}

	iThruster_HEU iThruster_HEUApp;

	iThruster_HEUApp.Run(sMOOSName.c_str(), sMissionFile.c_str());

	return 0;
}

