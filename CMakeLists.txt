cmake_minimum_required(VERSION 3.2)

project(iThruster_HEU)

# 开启所有Warning提示
add_compile_options(-Wall -Wextra)
set(CMAKE_CXX_FLAGS "-Wno-pmf-conversions")

# 指定编译器使用C/C++ 11
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

# 添加BlueSocket子目录
add_subdirectory(BlueSocket)

# 将指定目录添加到编译器的头文件搜索路径之下
include_directories(${PROJECT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/BlueSocket/include)

# 源文件集合
set(SRC
${PROJECT_SOURCE_DIR}/iThruster_HEU.cpp
${PROJECT_SOURCE_DIR}/Main.cpp)

# 使用指定的源文件来生成目标可执行文件
add_executable(iThruster_HEU ${SRC})

# 将目标文件与库文件进行链接
target_link_libraries(iThruster_HEU
${MOOS_LIBRARIES}
mbutil
pthread
BlueSocket)
